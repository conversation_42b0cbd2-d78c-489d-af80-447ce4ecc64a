{"name": "@lead-gen/backend", "version": "1.0.0", "description": "Backend API for Lead Generation Platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "bcrypt": "^5.1.0", "jsonwebtoken": "^9.0.1", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.3", "bull": "^4.11.3", "ioredis": "^5.3.2", "winston": "^3.10.0", "dotenv": "^16.3.1", "zod": "^3.21.4", "@lead-gen/shared": "file:../../packages/shared", "@lead-gen/database": "file:../../packages/database"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/compression": "^1.7.2", "@types/bcrypt": "^5.0.0", "@types/jsonwebtoken": "^9.0.2", "@types/passport": "^1.0.12", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/multer": "^1.4.7", "@types/nodemailer": "^6.4.8", "@types/node": "^20.4.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.1.6", "jest": "^29.6.1", "@types/jest": "^29.5.3", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@types/supertest": "^2.0.12"}, "engines": {"node": ">=18.0.0"}}