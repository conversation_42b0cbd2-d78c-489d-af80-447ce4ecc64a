import { Request, Response, NextFunction } from 'express';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { db } from '@lead-gen/database';
import { 
  LoginSchema, 
  RegisterSchema, 
  User, 
  UserRole, 
  SubscriptionPlan 
} from '@lead-gen/shared';
import { config } from '../config';
import { 
  AppError, 
  AuthenticationError, 
  ValidationError,
  catchAsync 
} from '../middleware/errorHandler';
import { logger } from '../utils/logger';

// Generate JWT token
const generateToken = (user: User): string => {
  return jwt.sign(
    {
      userId: user.id,
      email: user.email,
      role: user.role,
    },
    config.jwt.secret,
    {
      expiresIn: config.jwt.expiresIn,
    }
  );
};

// Generate refresh token
const generateRefreshToken = (user: User): string => {
  return jwt.sign(
    {
      userId: user.id,
      email: user.email,
    },
    config.jwt.refreshSecret,
    {
      expiresIn: config.jwt.refreshExpiresIn,
    }
  );
};

// Hash password
const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
};

// Compare password
const comparePassword = async (password: string, hash: string): Promise<boolean> => {
  return bcrypt.compare(password, hash);
};

// Register new user
export const register = catchAsync(async (req: Request, res: Response) => {
  // Validate request body
  const validatedData = RegisterSchema.parse(req.body);
  
  // Check if user already exists
  const existingUser = await db('users')
    .where({ email: validatedData.email })
    .first();
  
  if (existingUser) {
    throw new AppError('User already exists with this email', 409);
  }
  
  // Hash password
  const passwordHash = await hashPassword(validatedData.password);
  
  // Create user
  const [newUser] = await db('users')
    .insert({
      email: validatedData.email,
      password_hash: passwordHash,
      first_name: validatedData.firstName,
      last_name: validatedData.lastName,
      role: UserRole.USER,
      subscription_plan: SubscriptionPlan.FREE,
      preferences: {
        timezone: 'UTC',
        language: 'en',
        emailNotifications: true,
        pushNotifications: true,
        weeklyReports: true,
        theme: 'light',
      },
      limits: {
        leadsPerMonth: 100,
        emailsPerDay: 50,
        campaignsActive: 3,
        teamMembers: 1,
        apiCallsPerMonth: 1000,
      },
    })
    .returning('*');
  
  // Generate tokens
  const accessToken = generateToken(newUser);
  const refreshToken = generateRefreshToken(newUser);
  
  // Remove password hash from response
  const { password_hash, ...userResponse } = newUser;
  
  logger.info('User registered successfully', {
    userId: newUser.id,
    email: newUser.email,
  });
  
  res.status(201).json({
    success: true,
    data: {
      user: userResponse,
      tokens: {
        accessToken,
        refreshToken,
        expiresIn: config.jwt.expiresIn,
      },
    },
    message: 'User registered successfully',
  });
});

// Login user
export const login = catchAsync(async (req: Request, res: Response) => {
  // Validate request body
  const { email, password } = LoginSchema.parse(req.body);
  
  // Find user by email
  const user = await db('users')
    .where({ email })
    .first();
  
  if (!user) {
    throw new AuthenticationError('Invalid email or password');
  }
  
  // Check password
  const isPasswordValid = await comparePassword(password, user.password_hash);
  
  if (!isPasswordValid) {
    throw new AuthenticationError('Invalid email or password');
  }
  
  // Check if user is active
  if (user.deleted_at) {
    throw new AuthenticationError('Account has been deactivated');
  }
  
  // Update last login
  await db('users')
    .where({ id: user.id })
    .update({ last_login_at: new Date() });
  
  // Generate tokens
  const accessToken = generateToken(user);
  const refreshToken = generateRefreshToken(user);
  
  // Remove password hash from response
  const { password_hash, ...userResponse } = user;
  
  logger.info('User logged in successfully', {
    userId: user.id,
    email: user.email,
  });
  
  res.json({
    success: true,
    data: {
      user: userResponse,
      tokens: {
        accessToken,
        refreshToken,
        expiresIn: config.jwt.expiresIn,
      },
    },
    message: 'Login successful',
  });
});

// Refresh token
export const refreshToken = catchAsync(async (req: Request, res: Response) => {
  const { refreshToken } = req.body;
  
  if (!refreshToken) {
    throw new AuthenticationError('Refresh token required');
  }
  
  try {
    // Verify refresh token
    const decoded = jwt.verify(refreshToken, config.jwt.refreshSecret) as any;
    
    // Get user
    const user = await db('users')
      .where({ id: decoded.userId })
      .first();
    
    if (!user || user.deleted_at) {
      throw new AuthenticationError('Invalid refresh token');
    }
    
    // Generate new access token
    const newAccessToken = generateToken(user);
    
    res.json({
      success: true,
      data: {
        accessToken: newAccessToken,
        expiresIn: config.jwt.expiresIn,
      },
      message: 'Token refreshed successfully',
    });
  } catch (error) {
    throw new AuthenticationError('Invalid refresh token');
  }
});

// Get current user
export const getCurrentUser = catchAsync(async (req: Request, res: Response) => {
  if (!req.user) {
    throw new AuthenticationError();
  }
  
  // Remove sensitive data
  const { password_hash, ...userResponse } = req.user as any;
  
  res.json({
    success: true,
    data: { user: userResponse },
  });
});

// Logout user (client-side token removal)
export const logout = catchAsync(async (req: Request, res: Response) => {
  // In a more sophisticated setup, you might want to blacklist the token
  // For now, we'll just return success and let the client remove the token
  
  logger.info('User logged out', {
    userId: req.user?.id,
    email: req.user?.email,
  });
  
  res.json({
    success: true,
    message: 'Logout successful',
  });
});

// Change password
export const changePassword = catchAsync(async (req: Request, res: Response) => {
  if (!req.user) {
    throw new AuthenticationError();
  }
  
  const { currentPassword, newPassword } = req.body;
  
  if (!currentPassword || !newPassword) {
    throw new ValidationError('Current password and new password are required');
  }
  
  if (newPassword.length < 8) {
    throw new ValidationError('New password must be at least 8 characters long');
  }
  
  // Get user with password hash
  const user = await db('users')
    .where({ id: req.user.id })
    .first();
  
  if (!user) {
    throw new AuthenticationError('User not found');
  }
  
  // Verify current password
  const isCurrentPasswordValid = await comparePassword(currentPassword, user.password_hash);
  
  if (!isCurrentPasswordValid) {
    throw new AuthenticationError('Current password is incorrect');
  }
  
  // Hash new password
  const newPasswordHash = await hashPassword(newPassword);
  
  // Update password
  await db('users')
    .where({ id: user.id })
    .update({ password_hash: newPasswordHash });
  
  logger.info('Password changed successfully', {
    userId: user.id,
    email: user.email,
  });
  
  res.json({
    success: true,
    message: 'Password changed successfully',
  });
});
