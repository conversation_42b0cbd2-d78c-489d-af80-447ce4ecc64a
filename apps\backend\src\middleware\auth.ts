import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { db } from '@lead-gen/database';
import { User, UserRole } from '@lead-gen/shared';
import { config } from '../config';
import { AuthenticationError, AuthorizationError } from './errorHandler';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}

// JWT payload interface
interface JWTPayload {
  userId: string;
  email: string;
  role: UserRole;
  iat: number;
  exp: number;
}

// Extract token from request
const extractToken = (req: Request): string | null => {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Also check for token in cookies (for web app)
  if (req.cookies && req.cookies.token) {
    return req.cookies.token;
  }
  
  return null;
};

// Verify JWT token
const verifyToken = (token: string): Promise<JWTPayload> => {
  return new Promise((resolve, reject) => {
    jwt.verify(token, config.jwt.secret, (err, decoded) => {
      if (err) {
        reject(err);
      } else {
        resolve(decoded as JWTPayload);
      }
    });
  });
};

// Get user from database
const getUserById = async (userId: string): Promise<User | null> => {
  try {
    const user = await db('users')
      .where({ id: userId })
      .first();
    
    return user || null;
  } catch (error) {
    throw new Error('Failed to fetch user');
  }
};

// Main authentication middleware
export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Extract token
    const token = extractToken(req);
    
    if (!token) {
      throw new AuthenticationError('No token provided');
    }
    
    // Verify token
    const decoded = await verifyToken(token);
    
    // Get user from database
    const user = await getUserById(decoded.userId);
    
    if (!user) {
      throw new AuthenticationError('User not found');
    }
    
    // Check if user is active (not deleted/suspended)
    if (user.deletedAt) {
      throw new AuthenticationError('Account has been deactivated');
    }
    
    // Attach user to request
    req.user = user;
    
    next();
  } catch (error) {
    next(error);
  }
};

// Optional authentication middleware (doesn't throw if no token)
export const optionalAuthMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const token = extractToken(req);
    
    if (token) {
      const decoded = await verifyToken(token);
      const user = await getUserById(decoded.userId);
      
      if (user && !user.deletedAt) {
        req.user = user;
      }
    }
    
    next();
  } catch (error) {
    // Ignore authentication errors for optional auth
    next();
  }
};

// Role-based authorization middleware
export const requireRole = (...roles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new AuthenticationError());
    }
    
    if (!roles.includes(req.user.role)) {
      return next(new AuthorizationError('Insufficient permissions'));
    }
    
    next();
  };
};

// Admin only middleware
export const requireAdmin = requireRole(UserRole.ADMIN);

// Team lead or admin middleware
export const requireTeamLead = requireRole(UserRole.ADMIN, UserRole.TEAM_LEAD);

// Subscription plan middleware
export const requireSubscription = (...plans: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new AuthenticationError());
    }
    
    if (!plans.includes(req.user.subscriptionPlan)) {
      return next(new AuthorizationError('Subscription upgrade required'));
    }
    
    next();
  };
};

// Resource ownership middleware
export const requireOwnership = (resourceIdParam: string = 'id') => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return next(new AuthenticationError());
      }
      
      // Admin can access any resource
      if (req.user.role === UserRole.ADMIN) {
        return next();
      }
      
      const resourceId = req.params[resourceIdParam];
      
      if (!resourceId) {
        return next(new AuthorizationError('Resource ID required'));
      }
      
      // Check if user owns the resource
      // This is a generic check - specific implementations should override
      if (req.user.id !== resourceId) {
        return next(new AuthorizationError('Access denied'));
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
};

// Rate limiting by user
export const userRateLimit = (maxRequests: number, windowMs: number) => {
  const userRequests = new Map<string, { count: number; resetTime: number }>();
  
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next();
    }
    
    const userId = req.user.id;
    const now = Date.now();
    const userLimit = userRequests.get(userId);
    
    if (!userLimit || now > userLimit.resetTime) {
      userRequests.set(userId, {
        count: 1,
        resetTime: now + windowMs,
      });
      return next();
    }
    
    if (userLimit.count >= maxRequests) {
      return res.status(429).json({
        success: false,
        error: {
          message: 'Rate limit exceeded',
          code: 'RATE_LIMIT_ERROR',
          retryAfter: Math.ceil((userLimit.resetTime - now) / 1000),
        },
      });
    }
    
    userLimit.count++;
    next();
  };
};
