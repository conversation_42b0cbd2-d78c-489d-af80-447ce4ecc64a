import { api } from './client'
import { User, LoginCredentials, RegisterData, ApiResponse } from '@lead-gen/shared'

interface AuthResponse {
  user: User
  tokens: {
    accessToken: string
    refreshToken: string
    expiresIn: string
  }
}

interface RefreshTokenResponse {
  accessToken: string
  expiresIn: string
}

export const authApi = {
  // Login user
  login: async (credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> => {
    return api.post<AuthResponse>('/auth/login', credentials)
  },

  // Register user
  register: async (data: RegisterData): Promise<ApiResponse<AuthResponse>> => {
    return api.post<AuthResponse>('/auth/register', data)
  },

  // Refresh access token
  refreshToken: async (refreshToken: string): Promise<ApiResponse<RefreshTokenResponse>> => {
    return api.post<RefreshTokenResponse>('/auth/refresh', { refreshToken })
  },

  // Get current user
  getCurrentUser: async (): Promise<ApiResponse<{ user: User }>> => {
    return api.get<{ user: User }>('/auth/me')
  },

  // Logout user
  logout: async (): Promise<ApiResponse<void>> => {
    return api.post<void>('/auth/logout')
  },

  // Change password
  changePassword: async (data: {
    currentPassword: string
    newPassword: string
  }): Promise<ApiResponse<void>> => {
    return api.put<void>('/auth/change-password', data)
  },
}
