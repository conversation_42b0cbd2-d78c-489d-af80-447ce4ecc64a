import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ApiResponse } from '@lead-gen/shared'

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    // Get token from localStorage
    const authStorage = localStorage.getItem('auth-storage')
    if (authStorage) {
      try {
        const { state } = JSON.parse(authStorage)
        const token = state?.tokens?.accessToken
        
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
      } catch (error) {
        console.error('Error parsing auth storage:', error)
      }
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle token refresh
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  async (error) => {
    const originalRequest = error.config
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true
      
      try {
        // Get refresh token from localStorage
        const authStorage = localStorage.getItem('auth-storage')
        if (authStorage) {
          const { state } = JSON.parse(authStorage)
          const refreshToken = state?.tokens?.refreshToken
          
          if (refreshToken) {
            // Try to refresh the token
            const response = await axios.post(
              `${import.meta.env.VITE_API_URL || 'http://localhost:3001/api'}/auth/refresh`,
              { refreshToken }
            )
            
            if (response.data.success) {
              const newToken = response.data.data.accessToken
              
              // Update the token in localStorage
              const updatedState = {
                ...state,
                tokens: {
                  ...state.tokens,
                  accessToken: newToken,
                  expiresIn: response.data.data.expiresIn,
                },
              }
              
              localStorage.setItem('auth-storage', JSON.stringify({
                state: updatedState,
                version: 0,
              }))
              
              // Retry the original request with new token
              originalRequest.headers.Authorization = `Bearer ${newToken}`
              return apiClient(originalRequest)
            }
          }
        }
      } catch (refreshError) {
        // Refresh failed, clear auth and redirect to login
        localStorage.removeItem('auth-storage')
        window.location.href = '/login'
        return Promise.reject(refreshError)
      }
    }
    
    return Promise.reject(error)
  }
)

// Generic API request function
export const apiRequest = async <T = any>(
  config: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  try {
    const response = await apiClient(config)
    return response.data
  } catch (error: any) {
    if (error.response?.data) {
      return error.response.data
    }
    
    return {
      success: false,
      message: error.message || 'An unexpected error occurred',
      errors: [error.message || 'Network error'],
    }
  }
}

// HTTP methods
export const api = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    apiRequest<T>({ method: 'GET', url, ...config }),
    
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    apiRequest<T>({ method: 'POST', url, data, ...config }),
    
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    apiRequest<T>({ method: 'PUT', url, data, ...config }),
    
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    apiRequest<T>({ method: 'PATCH', url, data, ...config }),
    
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    apiRequest<T>({ method: 'DELETE', url, ...config }),
}

export default apiClient
