import { Users, Building2, Mail, TrendingUp } from 'lucide-react'

const stats = [
  {
    name: 'Total Contacts',
    value: '2,651',
    change: '+12%',
    changeType: 'positive',
    icon: Users,
  },
  {
    name: 'Companies',
    value: '847',
    change: '+8%',
    changeType: 'positive',
    icon: Building2,
  },
  {
    name: 'Active Campaigns',
    value: '12',
    change: '+2',
    changeType: 'positive',
    icon: Mail,
  },
  {
    name: 'Response Rate',
    value: '24.3%',
    change: '+4.2%',
    changeType: 'positive',
    icon: TrendingUp,
  },
]

export const DashboardPage = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-500">
          Welcome back! Here's what's happening with your lead generation.
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="card-content">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <stat.icon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">
                        {stat.value}
                      </div>
                      <div
                        className={`ml-2 flex items-baseline text-sm font-semibold ${
                          stat.changeType === 'positive'
                            ? 'text-green-600'
                            : 'text-red-600'
                        }`}
                      >
                        {stat.change}
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Recent Activity</h3>
            <p className="card-description">Latest actions in your campaigns</p>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    Email sent to John Smith
                  </p>
                  <p className="text-xs text-gray-500">2 minutes ago</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    New contact added: Sarah Johnson
                  </p>
                  <p className="text-xs text-gray-500">15 minutes ago</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    Campaign "Q4 Outreach" paused
                  </p>
                  <p className="text-xs text-gray-500">1 hour ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="card-title">Top Performing Campaigns</h3>
            <p className="card-description">Campaigns with highest response rates</p>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Tech Leaders Outreach
                  </p>
                  <p className="text-xs text-gray-500">245 contacts</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-green-600">32.1%</p>
                  <p className="text-xs text-gray-500">response rate</p>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Healthcare Decision Makers
                  </p>
                  <p className="text-xs text-gray-500">189 contacts</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-green-600">28.7%</p>
                  <p className="text-xs text-gray-500">response rate</p>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Startup Founders
                  </p>
                  <p className="text-xs text-gray-500">156 contacts</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-green-600">25.3%</p>
                  <p className="text-xs text-gray-500">response rate</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
