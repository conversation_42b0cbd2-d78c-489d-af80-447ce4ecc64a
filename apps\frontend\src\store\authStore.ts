import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User } from '@lead-gen/shared'
import { authApi } from '../api/authApi'
import toast from 'react-hot-toast'

interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: string
}

interface AuthState {
  user: User | null
  tokens: AuthTokens | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Actions
  login: (email: string, password: string) => Promise<void>
  register: (data: {
    email: string
    password: string
    firstName: string
    lastName: string
    company?: string
  }) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
  getCurrentUser: () => Promise<void>
  updateUser: (userData: Partial<User>) => void
  clearAuth: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      tokens: null,
      isAuthenticated: false,
      isLoading: true,

      login: async (email: string, password: string) => {
        try {
          set({ isLoading: true })
          
          const response = await authApi.login({ email, password })
          
          if (response.success && response.data) {
            const { user, tokens } = response.data
            
            set({
              user,
              tokens,
              isAuthenticated: true,
              isLoading: false,
            })
            
            toast.success('Login successful!')
          } else {
            throw new Error(response.message || 'Login failed')
          }
        } catch (error: any) {
          set({ isLoading: false })
          const message = error.response?.data?.error?.message || error.message || 'Login failed'
          toast.error(message)
          throw error
        }
      },

      register: async (data) => {
        try {
          set({ isLoading: true })
          
          const response = await authApi.register(data)
          
          if (response.success && response.data) {
            const { user, tokens } = response.data
            
            set({
              user,
              tokens,
              isAuthenticated: true,
              isLoading: false,
            })
            
            toast.success('Registration successful!')
          } else {
            throw new Error(response.message || 'Registration failed')
          }
        } catch (error: any) {
          set({ isLoading: false })
          const message = error.response?.data?.error?.message || error.message || 'Registration failed'
          toast.error(message)
          throw error
        }
      },

      logout: () => {
        try {
          authApi.logout()
        } catch (error) {
          // Ignore logout errors
        }
        
        set({
          user: null,
          tokens: null,
          isAuthenticated: false,
          isLoading: false,
        })
        
        toast.success('Logged out successfully')
      },

      refreshToken: async () => {
        try {
          const { tokens } = get()
          
          if (!tokens?.refreshToken) {
            throw new Error('No refresh token available')
          }
          
          const response = await authApi.refreshToken(tokens.refreshToken)
          
          if (response.success && response.data) {
            set((state) => ({
              tokens: {
                ...state.tokens!,
                accessToken: response.data!.accessToken,
                expiresIn: response.data!.expiresIn,
              },
            }))
          } else {
            throw new Error('Token refresh failed')
          }
        } catch (error) {
          // If refresh fails, logout the user
          get().logout()
          throw error
        }
      },

      getCurrentUser: async () => {
        try {
          set({ isLoading: true })
          
          const response = await authApi.getCurrentUser()
          
          if (response.success && response.data) {
            set({
              user: response.data.user,
              isAuthenticated: true,
              isLoading: false,
            })
          } else {
            throw new Error('Failed to get current user')
          }
        } catch (error) {
          set({
            user: null,
            tokens: null,
            isAuthenticated: false,
            isLoading: false,
          })
        }
      },

      updateUser: (userData: Partial<User>) => {
        set((state) => ({
          user: state.user ? { ...state.user, ...userData } : null,
        }))
      },

      clearAuth: () => {
        set({
          user: null,
          tokens: null,
          isAuthenticated: false,
          isLoading: false,
        })
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Check if we have tokens and try to get current user
          if (state.tokens?.accessToken) {
            state.getCurrentUser().catch(() => {
              // If getting current user fails, clear auth
              state.clearAuth()
            })
          } else {
            state.isLoading = false
          }
        }
      },
    }
  )
)
