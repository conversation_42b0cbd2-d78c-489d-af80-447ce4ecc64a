version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: leadgen-postgres-dev
    environment:
      POSTGRES_DB: leadgen_dev
      POSTGRES_USER: leadgen_user
      POSTGRES_PASSWORD: leadgen_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./packages/database/init:/docker-entrypoint-initdb.d
    networks:
      - leadgen-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U leadgen_user -d leadgen_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: leadgen-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - leadgen-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API (Development)
  backend:
    build:
      context: .
      dockerfile: apps/backend/Dockerfile.dev
    container_name: leadgen-backend-dev
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************************/leadgen_dev
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production-min-32-chars
      - JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production-min-32-chars
    ports:
      - "3001:3001"
    volumes:
      - ./apps/backend:/app/apps/backend
      - ./packages:/app/packages
      - /app/node_modules
    networks:
      - leadgen-dev-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: npm run dev

  # Frontend (Development)
  frontend:
    build:
      context: .
      dockerfile: apps/frontend/Dockerfile.dev
    container_name: leadgen-frontend-dev
    environment:
      - VITE_API_URL=http://localhost:3001/api
    ports:
      - "3000:3000"
    volumes:
      - ./apps/frontend:/app/apps/frontend
      - ./packages:/app/packages
      - /app/node_modules
    networks:
      - leadgen-dev-network
    depends_on:
      - backend
    command: npm run dev

volumes:
  postgres_dev_data:
  redis_dev_data:

networks:
  leadgen-dev-network:
    driver: bridge
