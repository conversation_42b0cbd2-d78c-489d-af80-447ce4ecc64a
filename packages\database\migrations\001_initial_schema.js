/**
 * Initial database schema for Lead Generation Platform
 * Creates core tables: users, companies, contacts, campaigns, and related tables
 */

exports.up = async function(knex) {
  // Users table
  await knex.schema.createTable('users', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('email').unique().notNullable();
    table.string('password_hash').notNullable();
    table.string('first_name').notNullable();
    table.string('last_name').notNullable();
    table.string('avatar');
    table.enum('role', ['admin', 'user', 'team_lead', 'viewer']).defaultTo('user');
    table.enum('subscription_plan', ['free', 'starter', 'professional', 'enterprise']).defaultTo('free');
    table.boolean('is_email_verified').defaultTo(false);
    table.timestamp('last_login_at');
    table.jsonb('preferences').defaultTo('{}');
    table.jsonb('limits').defaultTo('{}');
    table.jsonb('location');
    table.timestamps(true, true);
    
    table.index(['email']);
    table.index(['role']);
    table.index(['subscription_plan']);
  });

  // Companies table
  await knex.schema.createTable('companies', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('name').notNullable();
    table.string('domain').unique();
    table.text('description');
    table.enum('industry', [
      'technology', 'healthcare', 'finance', 'education', 'retail',
      'manufacturing', 'real_estate', 'consulting', 'marketing', 'other'
    ]);
    table.enum('size', ['1-10', '11-50', '51-200', '201-1000', '1000+']);
    table.integer('founded');
    table.bigInteger('revenue');
    table.integer('employees');
    table.string('website');
    table.string('logo');
    table.jsonb('social_profiles').defaultTo('[]');
    table.jsonb('contact_info').defaultTo('{}');
    table.jsonb('technologies').defaultTo('[]');
    table.jsonb('funding');
    table.jsonb('metrics').defaultTo('{}');
    table.jsonb('enrichment_data');
    table.jsonb('location');
    table.timestamps(true, true);
    
    table.index(['domain']);
    table.index(['industry']);
    table.index(['size']);
    table.index(['name']);
  });

  // Contacts table
  await knex.schema.createTable('contacts', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('first_name').notNullable();
    table.string('last_name').notNullable();
    table.string('email');
    table.string('phone');
    table.string('job_title');
    table.string('department');
    table.enum('seniority', [
      'entry', 'junior', 'senior', 'manager', 'director', 'vp', 'c_level', 'founder'
    ]);
    table.uuid('company_id').references('id').inTable('companies').onDelete('SET NULL');
    table.string('linkedin_url');
    table.string('twitter_url');
    table.string('avatar');
    table.jsonb('location');
    table.jsonb('social_profiles').defaultTo('[]');
    table.enum('status', [
      'new', 'contacted', 'responded', 'qualified', 'converted', 'unqualified', 'do_not_contact'
    ]).defaultTo('new');
    table.integer('score').defaultTo(0);
    table.jsonb('tags').defaultTo('[]');
    table.text('notes');
    table.timestamp('last_contacted_at');
    table.jsonb('enrichment_data');
    table.jsonb('email_validation');
    table.jsonb('preferences').defaultTo('{}');
    table.timestamps(true, true);
    
    table.index(['email']);
    table.index(['company_id']);
    table.index(['status']);
    table.index(['job_title']);
    table.index(['seniority']);
    table.index(['score']);
  });

  // Campaigns table
  await knex.schema.createTable('campaigns', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.string('name').notNullable();
    table.text('description');
    table.enum('type', ['email', 'linkedin', 'phone', 'multi_channel']).defaultTo('email');
    table.enum('status', ['draft', 'active', 'paused', 'completed', 'archived']).defaultTo('draft');
    table.jsonb('settings').defaultTo('{}');
    table.jsonb('targeting_criteria').defaultTo('{}');
    table.integer('total_contacts').defaultTo(0);
    table.integer('contacted_count').defaultTo(0);
    table.integer('responded_count').defaultTo(0);
    table.integer('converted_count').defaultTo(0);
    table.timestamp('started_at');
    table.timestamp('completed_at');
    table.timestamps(true, true);
    
    table.index(['user_id']);
    table.index(['status']);
    table.index(['type']);
  });

  // Outreach sequences table
  await knex.schema.createTable('outreach_sequences', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('campaign_id').references('id').inTable('campaigns').onDelete('CASCADE');
    table.integer('step_number').notNullable();
    table.string('name').notNullable();
    table.enum('channel', ['email', 'linkedin', 'phone', 'twitter', 'facebook']).defaultTo('email');
    table.text('subject'); // For email
    table.text('message_template').notNullable();
    table.integer('delay_days').defaultTo(0);
    table.integer('delay_hours').defaultTo(0);
    table.boolean('is_active').defaultTo(true);
    table.jsonb('settings').defaultTo('{}');
    table.timestamps(true, true);
    
    table.index(['campaign_id']);
    table.index(['step_number']);
    table.unique(['campaign_id', 'step_number']);
  });

  // Lead interactions table
  await knex.schema.createTable('lead_interactions', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('contact_id').references('id').inTable('contacts').onDelete('CASCADE');
    table.uuid('campaign_id').references('id').inTable('campaigns').onDelete('CASCADE');
    table.uuid('sequence_id').references('id').inTable('outreach_sequences').onDelete('SET NULL');
    table.enum('type', [
      'email_sent', 'email_opened', 'email_clicked', 'email_replied', 'email_bounced',
      'linkedin_sent', 'linkedin_viewed', 'linkedin_replied',
      'phone_called', 'phone_answered', 'phone_voicemail',
      'meeting_scheduled', 'meeting_completed'
    ]).notNullable();
    table.text('content');
    table.text('response');
    table.jsonb('metadata').defaultTo('{}');
    table.timestamp('interaction_at').defaultTo(knex.fn.now());
    table.timestamps(true, true);
    
    table.index(['contact_id']);
    table.index(['campaign_id']);
    table.index(['type']);
    table.index(['interaction_at']);
  });

  // Campaign contacts (many-to-many relationship)
  await knex.schema.createTable('campaign_contacts', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('campaign_id').references('id').inTable('campaigns').onDelete('CASCADE');
    table.uuid('contact_id').references('id').inTable('contacts').onDelete('CASCADE');
    table.enum('status', ['pending', 'active', 'completed', 'paused', 'excluded']).defaultTo('pending');
    table.integer('current_step').defaultTo(0);
    table.timestamp('next_action_at');
    table.timestamp('added_at').defaultTo(knex.fn.now());
    table.timestamps(true, true);
    
    table.unique(['campaign_id', 'contact_id']);
    table.index(['campaign_id']);
    table.index(['contact_id']);
    table.index(['status']);
    table.index(['next_action_at']);
  });
};

exports.down = async function(knex) {
  await knex.schema.dropTableIfExists('campaign_contacts');
  await knex.schema.dropTableIfExists('lead_interactions');
  await knex.schema.dropTableIfExists('outreach_sequences');
  await knex.schema.dropTableIfExists('campaigns');
  await knex.schema.dropTableIfExists('contacts');
  await knex.schema.dropTableIfExists('companies');
  await knex.schema.dropTableIfExists('users');
};
