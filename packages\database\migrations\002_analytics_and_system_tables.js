/**
 * Analytics and system tables for Lead Generation Platform
 * Creates tables for analytics, API keys, webhooks, and system logs
 */

exports.up = async function(knex) {
  // API keys table for external integrations
  await knex.schema.createTable('api_keys', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.string('name').notNullable();
    table.string('key_hash').notNullable();
    table.string('key_prefix', 8).notNullable();
    table.jsonb('permissions').defaultTo('[]');
    table.boolean('is_active').defaultTo(true);
    table.timestamp('last_used_at');
    table.timestamp('expires_at');
    table.timestamps(true, true);
    
    table.index(['user_id']);
    table.index(['key_prefix']);
    table.index(['is_active']);
  });

  // Webhooks table
  await knex.schema.createTable('webhooks', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.string('name').notNullable();
    table.string('url').notNullable();
    table.jsonb('events').defaultTo('[]');
    table.string('secret');
    table.boolean('is_active').defaultTo(true);
    table.integer('retry_count').defaultTo(3);
    table.timestamp('last_triggered_at');
    table.timestamps(true, true);
    
    table.index(['user_id']);
    table.index(['is_active']);
  });

  // Analytics events table
  await knex.schema.createTable('analytics_events', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.string('event_type').notNullable();
    table.string('event_name').notNullable();
    table.jsonb('properties').defaultTo('{}');
    table.string('session_id');
    table.string('ip_address');
    table.string('user_agent');
    table.timestamp('event_at').defaultTo(knex.fn.now());
    table.timestamps(true, true);
    
    table.index(['user_id']);
    table.index(['event_type']);
    table.index(['event_name']);
    table.index(['event_at']);
  });

  // Daily analytics aggregations
  await knex.schema.createTable('daily_analytics', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.date('date').notNullable();
    table.integer('leads_generated').defaultTo(0);
    table.integer('emails_sent').defaultTo(0);
    table.integer('emails_opened').defaultTo(0);
    table.integer('emails_clicked').defaultTo(0);
    table.integer('emails_replied').defaultTo(0);
    table.integer('linkedin_messages_sent').defaultTo(0);
    table.integer('linkedin_messages_replied').defaultTo(0);
    table.integer('phone_calls_made').defaultTo(0);
    table.integer('meetings_scheduled').defaultTo(0);
    table.integer('leads_converted').defaultTo(0);
    table.decimal('response_rate', 5, 2).defaultTo(0);
    table.decimal('conversion_rate', 5, 2).defaultTo(0);
    table.timestamps(true, true);
    
    table.unique(['user_id', 'date']);
    table.index(['user_id']);
    table.index(['date']);
  });

  // Email templates table
  await knex.schema.createTable('email_templates', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.string('name').notNullable();
    table.text('description');
    table.string('subject').notNullable();
    table.text('html_content');
    table.text('text_content');
    table.jsonb('variables').defaultTo('[]');
    table.boolean('is_public').defaultTo(false);
    table.integer('usage_count').defaultTo(0);
    table.decimal('avg_open_rate', 5, 2);
    table.decimal('avg_click_rate', 5, 2);
    table.decimal('avg_reply_rate', 5, 2);
    table.timestamps(true, true);
    
    table.index(['user_id']);
    table.index(['is_public']);
    table.index(['usage_count']);
  });
};

exports.down = async function(knex) {
  await knex.schema.dropTableIfExists('email_templates');
  await knex.schema.dropTableIfExists('daily_analytics');
  await knex.schema.dropTableIfExists('analytics_events');
  await knex.schema.dropTableIfExists('webhooks');
  await knex.schema.dropTableIfExists('api_keys');
};
