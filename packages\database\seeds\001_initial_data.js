/**
 * Initial seed data for Lead Generation Platform
 * Creates sample users, companies, contacts, and email templates
 */

const bcrypt = require('bcrypt');

exports.seed = async function(knex) {
  // Clear existing data
  await knex('email_templates').del();
  await knex('daily_analytics').del();
  await knex('analytics_events').del();
  await knex('webhooks').del();
  await knex('api_keys').del();
  await knex('campaign_contacts').del();
  await knex('lead_interactions').del();
  await knex('outreach_sequences').del();
  await knex('campaigns').del();
  await knex('contacts').del();
  await knex('companies').del();
  await knex('users').del();

  // Create admin user
  const adminUserId = '550e8400-e29b-41d4-a716-446655440000';
  const hashedPassword = await bcrypt.hash('admin123', 10);
  
  await knex('users').insert([
    {
      id: adminUserId,
      email: '<EMAIL>',
      password_hash: hashedPassword,
      first_name: 'Admin',
      last_name: 'User',
      role: 'admin',
      subscription_plan: 'enterprise',
      is_email_verified: true,
      preferences: {
        timezone: 'UTC',
        language: 'en',
        emailNotifications: true,
        pushNotifications: true,
        weeklyReports: true,
        theme: 'light'
      },
      limits: {
        leadsPerMonth: -1, // Unlimited
        emailsPerDay: -1,
        campaignsActive: -1,
        teamMembers: -1,
        apiCallsPerMonth: -1
      }
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440001',
      email: '<EMAIL>',
      password_hash: await bcrypt.hash('demo123', 10),
      first_name: 'Demo',
      last_name: 'User',
      role: 'user',
      subscription_plan: 'professional',
      is_email_verified: true,
      preferences: {
        timezone: 'America/New_York',
        language: 'en',
        emailNotifications: true,
        pushNotifications: false,
        weeklyReports: true,
        theme: 'dark'
      },
      limits: {
        leadsPerMonth: 5000,
        emailsPerDay: 200,
        campaignsActive: 10,
        teamMembers: 5,
        apiCallsPerMonth: 10000
      }
    }
  ]);

  // Create sample companies
  const companies = [
    {
      id: '660e8400-e29b-41d4-a716-446655440000',
      name: 'TechCorp Solutions',
      domain: 'techcorp.com',
      description: 'Leading technology solutions provider',
      industry: 'technology',
      size: '201-1000',
      founded: 2015,
      revenue: 50000000,
      employees: 350,
      website: 'https://techcorp.com',
      social_profiles: [
        { platform: 'linkedin', url: 'https://linkedin.com/company/techcorp' },
        { platform: 'twitter', url: 'https://twitter.com/techcorp' }
      ],
      contact_info: {
        email: '<EMAIL>',
        phone: '******-0123'
      },
      technologies: ['React', 'Node.js', 'AWS', 'PostgreSQL'],
      location: {
        country: 'United States',
        state: 'California',
        city: 'San Francisco'
      }
    },
    {
      id: '660e8400-e29b-41d4-a716-446655440001',
      name: 'HealthTech Innovations',
      domain: 'healthtech.com',
      description: 'Healthcare technology and digital solutions',
      industry: 'healthcare',
      size: '51-200',
      founded: 2018,
      revenue: 15000000,
      employees: 120,
      website: 'https://healthtech.com',
      social_profiles: [
        { platform: 'linkedin', url: 'https://linkedin.com/company/healthtech' }
      ],
      contact_info: {
        email: '<EMAIL>',
        phone: '******-0456'
      },
      technologies: ['Python', 'Django', 'React', 'Docker'],
      location: {
        country: 'United States',
        state: 'Massachusetts',
        city: 'Boston'
      }
    }
  ];

  await knex('companies').insert(companies);

  // Create sample contacts
  const contacts = [
    {
      id: '770e8400-e29b-41d4-a716-446655440000',
      first_name: 'John',
      last_name: 'Smith',
      email: '<EMAIL>',
      phone: '******-0123',
      job_title: 'Chief Technology Officer',
      department: 'Engineering',
      seniority: 'c_level',
      company_id: '660e8400-e29b-41d4-a716-446655440000',
      linkedin_url: 'https://linkedin.com/in/johnsmith',
      status: 'new',
      score: 85,
      tags: ['decision_maker', 'technical'],
      preferences: {
        emailOptOut: false,
        phoneOptOut: false,
        linkedinOptOut: false,
        doNotContact: false,
        preferredContactMethod: 'email'
      }
    },
    {
      id: '770e8400-e29b-41d4-a716-446655440001',
      first_name: 'Sarah',
      last_name: 'Johnson',
      email: '<EMAIL>',
      phone: '******-0789',
      job_title: 'VP of Product',
      department: 'Product',
      seniority: 'vp',
      company_id: '660e8400-e29b-41d4-a716-446655440001',
      linkedin_url: 'https://linkedin.com/in/sarahjohnson',
      status: 'new',
      score: 92,
      tags: ['product_leader', 'healthcare'],
      preferences: {
        emailOptOut: false,
        phoneOptOut: true,
        linkedinOptOut: false,
        doNotContact: false,
        preferredContactMethod: 'linkedin'
      }
    }
  ];

  await knex('contacts').insert(contacts);

  // Create sample email templates
  const emailTemplates = [
    {
      id: '880e8400-e29b-41d4-a716-446655440000',
      user_id: adminUserId,
      name: 'Cold Outreach - Technology Leaders',
      description: 'Initial outreach template for technology decision makers',
      subject: 'Quick question about {{company_name}}\'s tech stack',
      html_content: `
        <p>Hi {{first_name}},</p>
        
        <p>I noticed that {{company_name}} is doing some interesting work in {{industry}}. 
        I'm particularly impressed by your recent growth and innovation.</p>
        
        <p>I'm reaching out because we've helped similar companies like yours optimize their 
        lead generation process and increase conversion rates by 40-60%.</p>
        
        <p>Would you be open to a brief 15-minute conversation to discuss how we might 
        help {{company_name}} achieve similar results?</p>
        
        <p>Best regards,<br>
        {{sender_name}}</p>
      `,
      text_content: `Hi {{first_name}},

I noticed that {{company_name}} is doing some interesting work in {{industry}}. I'm particularly impressed by your recent growth and innovation.

I'm reaching out because we've helped similar companies like yours optimize their lead generation process and increase conversion rates by 40-60%.

Would you be open to a brief 15-minute conversation to discuss how we might help {{company_name}} achieve similar results?

Best regards,
{{sender_name}}`,
      variables: ['first_name', 'company_name', 'industry', 'sender_name'],
      is_public: true,
      usage_count: 0
    },
    {
      id: '880e8400-e29b-41d4-a716-446655440001',
      user_id: adminUserId,
      name: 'Follow-up - No Response',
      description: 'Follow-up template for contacts who haven\'t responded',
      subject: 'Following up on {{company_name}}',
      html_content: `
        <p>Hi {{first_name}},</p>
        
        <p>I wanted to follow up on my previous email about helping {{company_name}} 
        improve your lead generation results.</p>
        
        <p>I understand you're probably busy, but I thought you might be interested 
        in a quick case study of how we helped a similar {{industry}} company 
        increase their qualified leads by 150% in just 3 months.</p>
        
        <p>Would a brief 10-minute call work for you this week?</p>
        
        <p>Best,<br>
        {{sender_name}}</p>
      `,
      text_content: `Hi {{first_name}},

I wanted to follow up on my previous email about helping {{company_name}} improve your lead generation results.

I understand you're probably busy, but I thought you might be interested in a quick case study of how we helped a similar {{industry}} company increase their qualified leads by 150% in just 3 months.

Would a brief 10-minute call work for you this week?

Best,
{{sender_name}}`,
      variables: ['first_name', 'company_name', 'industry', 'sender_name'],
      is_public: true,
      usage_count: 0
    }
  ];

  await knex('email_templates').insert(emailTemplates);
};
