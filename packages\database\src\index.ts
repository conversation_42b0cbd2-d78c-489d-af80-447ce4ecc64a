import knex, { <PERSON><PERSON> } from 'knex';
import config from '../knexfile';

// Get the appropriate configuration based on environment
const environment = process.env.NODE_ENV || 'development';
const knexConfig = config[environment as keyof typeof config];

// Create the database connection
export const db: Knex = knex(knexConfig);

// Database utility functions
export class DatabaseUtils {
  /**
   * Test database connection
   */
  static async testConnection(): Promise<boolean> {
    try {
      await db.raw('SELECT 1');
      return true;
    } catch (error) {
      console.error('Database connection failed:', error);
      return false;
    }
  }

  /**
   * Run database migrations
   */
  static async migrate(): Promise<void> {
    try {
      await db.migrate.latest();
      console.log('Database migrations completed successfully');
    } catch (error) {
      console.error('Database migration failed:', error);
      throw error;
    }
  }

  /**
   * Rollback database migrations
   */
  static async rollback(): Promise<void> {
    try {
      await db.migrate.rollback();
      console.log('Database rollback completed successfully');
    } catch (error) {
      console.error('Database rollback failed:', error);
      throw error;
    }
  }

  /**
   * Run database seeds
   */
  static async seed(): Promise<void> {
    try {
      await db.seed.run();
      console.log('Database seeding completed successfully');
    } catch (error) {
      console.error('Database seeding failed:', error);
      throw error;
    }
  }

  /**
   * Close database connection
   */
  static async close(): Promise<void> {
    try {
      await db.destroy();
      console.log('Database connection closed');
    } catch (error) {
      console.error('Error closing database connection:', error);
      throw error;
    }
  }

  /**
   * Execute a transaction
   */
  static async transaction<T>(
    callback: (trx: Knex.Transaction) => Promise<T>
  ): Promise<T> {
    return db.transaction(callback);
  }

  /**
   * Check if a table exists
   */
  static async tableExists(tableName: string): Promise<boolean> {
    try {
      const exists = await db.schema.hasTable(tableName);
      return exists;
    } catch (error) {
      console.error(`Error checking if table ${tableName} exists:`, error);
      return false;
    }
  }

  /**
   * Get table information
   */
  static async getTableInfo(tableName: string): Promise<any[]> {
    try {
      const info = await db(tableName).columnInfo();
      return Object.entries(info).map(([column, details]) => ({
        column,
        ...details,
      }));
    } catch (error) {
      console.error(`Error getting table info for ${tableName}:`, error);
      throw error;
    }
  }

  /**
   * Execute raw SQL query
   */
  static async raw(query: string, bindings?: any[]): Promise<any> {
    try {
      return await db.raw(query, bindings);
    } catch (error) {
      console.error('Error executing raw query:', error);
      throw error;
    }
  }
}

// Export the knex instance as default
export default db;

// Export types
export type { Knex } from 'knex';
